{"name": "BH-CRM", "version": "0.0.2", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "build": "vite build", "build:netlify": "npx convex deploy --cmd 'npm run build'", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build", "tsc": "tsc --jsx react-jsx --esModuleInterop --downlevelIteration"}, "dependencies": {"@convex-dev/auth": "^0.0.80", "@convex-dev/twilio": "^0.1.7", "@mapbox/search-js-core": "^1.2.0", "@react-pdf/renderer": "^4.3.0", "@types/mapbox-gl": "^3.4.1", "clsx": "^2.1.1", "convex": "^1.24.8", "crypto-js": "^4.2.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "mapbox-gl": "^3.13.0", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "resend": "^4.6.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tsx": "^4.20.3", "usehooks-ts": "^3.1.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/bun": "latest", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.32", "@types/nodemailer": "^6.4.14", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "~10", "convex-helpers": "^0.1.94", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "postcss": "~8", "prettier": "^3.5.3", "tailwindcss": "~3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}